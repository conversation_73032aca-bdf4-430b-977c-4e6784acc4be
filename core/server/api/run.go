package api

import (
	"crypto/tls"
	"crypto/x509"
	"fmt"
	"log"
	"net/http"
	"os"
	"strings"

	"github.com/gin-gonic/gin"
)

var jwtSecret []byte

func init() {
	jwtSecret = []byte(os.Getenv("JWT_SECRET"))

	if isMaintenanceMode() {
		log.Println("🔧 API MAINTENANCE MODE: ENABLED - All API endpoints are disabled")
	} else {
		log.Println("✅ API MAINTENANCE MODE: DISABLED - All API endpoints are active")
	}
}

func isMaintenanceMode() bool {
	// Check if all APIs are disabled
	_, err := os.Stat("/tmp/api_maintenance/all")
	return err == nil
}

func isAPITypeMaintenance(apiType string) bool {
	// Check if all APIs are disabled
	if isMaintenanceMode() {
		return true
	}

	// Check if specific API type is disabled
	_, err := os.Stat("/tmp/api_maintenance/" + apiType)
	return err == nil
}

func init() {
	go func() {
		// Load CA cert
		caCert, err := os.ReadFile("../certs/ca.pem")
		if err != nil {
			panic(err)
		}
		caPool := x509.NewCertPool()
		if !caPool.AppendCertsFromPEM(caCert) {
			panic("Failed to append CA cert")
		}

		tlsConfig := &tls.Config{
			ClientCAs: caPool,
			// Change as needed: tls.NoClientCert or tls.RequireAndVerifyClientCert
			ClientAuth: tls.RequireAndVerifyClientCert,
			MinVersion: tls.VersionTLS12,
		}

		// Gin setup for API
		gin.SetMode(gin.DebugMode)
		router := gin.Default()
		router.Use(gin.Logger())

		// Apply middleware to your API routes
		apiGroup := router.Group("/api")
		apiGroup.Use(maintenanceMiddleware())
		apiGroup.Use(jwtAuthMiddleware())
		initGetRequests(apiGroup)
		initPostRequests(apiGroup)

		srv := &http.Server{
			Addr:      ":8080",
			Handler:   router,
			TLSConfig: tlsConfig,
		}

		log.Println("Starting secure server with API on https://localhost:8080")
		if err := srv.ListenAndServeTLS("../certs/server.pem", "../certs/server.key"); err != nil {
			log.Fatal(err)
		}
	}()
}

// Maintenance middleware for API routes
func maintenanceMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		path := c.Request.URL.Path
		var apiType string

		// Determine API type based on path
		if strings.HasPrefix(path, "/api/players") {
			apiType = "players"
		} else if strings.HasPrefix(path, "/api/factions") {
			apiType = "factions"
		} else if strings.HasPrefix(path, "/api/jackpot") {
			apiType = "jackpot"
		} else {
			apiType = "unknown"
		}

		if isAPITypeMaintenance(apiType) {
			c.AbortWithStatusJSON(http.StatusServiceUnavailable, gin.H{
				"error": fmt.Sprintf("%s API is currently under maintenance", strings.Title(apiType)),
				"message": "Please try again later",
				"api_type": apiType,
			})
			return
		}
		c.Next()
	}
}

// JWT middleware for Gin API routes
func jwtAuthMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		auth := c.GetHeader("Authorization")
		if !strings.HasPrefix(auth, "Bearer ") {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "Missing JWT"})
			return
		}
		tokenStr := strings.TrimPrefix(auth, "Bearer ")
		if tokenStr != string(jwtSecret) {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "Invalid JWT"})
			return
		}
		c.Next()
	}
}
