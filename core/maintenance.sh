#!/bin/bash

# API Maintenance Mode Controller
# Usage: ./maintenance.sh [enable|disable|status] [api_type]

MAINTENANCE_DIR="/tmp/api_maintenance"

# Available API types
API_TYPES=("all" "players" "factions" "jackpot")

show_usage() {
    echo "Usage: $0 [enable|disable|status] [api_type]"
    echo ""
    echo "Commands:"
    echo "  enable [type]  - Enable maintenance mode for specific API type or all"
    echo "  disable [type] - Disable maintenance mode for specific API type or all"
    echo "  status         - Check current maintenance status"
    echo ""
    echo "API Types:"
    echo "  all      - All API endpoints"
    echo "  players  - Player-related endpoints (/api/players/*)"
    echo "  factions - Faction-related endpoints (/api/factions/*)"
    echo "  jackpot  - Jackpot endpoint (/api/jackpot)"
    echo ""
    echo "Examples:"
    echo "  $0 enable all       # Disable all APIs"
    echo "  $0 enable jackpot   # Disable only jackpot API"
    echo "  $0 disable players  # Enable players API"
    echo "  $0 status           # Check what's disabled"
}

create_maintenance_dir() {
    mkdir -p "$MAINTENANCE_DIR"
}

case "$1" in
    "enable")
        API_TYPE="${2:-all}"
        if [[ ! " ${API_TYPES[@]} " =~ " ${API_TYPE} " ]]; then
            echo "❌ Invalid API type: $API_TYPE"
            show_usage
            exit 1
        fi

        create_maintenance_dir
        touch "$MAINTENANCE_DIR/$API_TYPE"
        echo "🔧 API MAINTENANCE MODE: ENABLED for $API_TYPE"
        echo "   File created: $MAINTENANCE_DIR/$API_TYPE"
        ;;

    "disable")
        API_TYPE="${2:-all}"
        if [[ ! " ${API_TYPES[@]} " =~ " ${API_TYPE} " ]]; then
            echo "❌ Invalid API type: $API_TYPE"
            show_usage
            exit 1
        fi

        rm -f "$MAINTENANCE_DIR/$API_TYPE"
        echo "✅ API MAINTENANCE MODE: DISABLED for $API_TYPE"
        echo "   File removed: $MAINTENANCE_DIR/$API_TYPE"

        # Remove directory if empty
        if [ -d "$MAINTENANCE_DIR" ] && [ -z "$(ls -A $MAINTENANCE_DIR)" ]; then
            rmdir "$MAINTENANCE_DIR"
        fi
        ;;

    "status")
        echo "API Maintenance Status:"
        echo "======================"

        if [ ! -d "$MAINTENANCE_DIR" ]; then
            echo "✅ All APIs: ACTIVE"
            exit 0
        fi

        for api_type in "${API_TYPES[@]}"; do
            if [ -f "$MAINTENANCE_DIR/$api_type" ]; then
                echo "🔧 $api_type: DISABLED"
            else
                echo "✅ $api_type: ACTIVE"
            fi
        done
        ;;

    *)
        show_usage
        exit 1
        ;;
esac
